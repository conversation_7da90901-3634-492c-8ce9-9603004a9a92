import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  AlertTriangle,
  CheckCircle,
  ArrowRight,
  Shield,
  Play,
  ChevronLeft,
  ChevronRight,
  Globe,
  TrendingUp,
  Heart,
  Users,
  Clock,
  Zap,
  Activity,
  MapPin,
  RefreshCw,
  Target,
  Calendar,
  MapIcon
} from 'lucide-react';

// Components
import Header from '../components/Layout/Header';
import Footer from '../components/Layout/Footer';
import ChatWidget from '../components/Chat/ChatWidget';
import SimpleLeafletMap from '../components/Map/SimpleLeafletMap';

import { useDisasterData } from '../hooks/useDisasterData';
import { useAuth } from '../hooks/useAuth';
import { useRoles } from '../hooks/useRoles';

const Home: React.FC = () => {
  const [currentFeatureSlide, setCurrentFeatureSlide] = useState(0);
  const [showScrollTop, setShowScrollTop] = useState(false);

  // Auth and roles
  const { isAuthenticated } = useAuth();
  const { isAdmin, isCj, isOnlyUser } = useRoles();

  // Real-world disaster data
  const { disasters, loading: disastersLoading, error: disastersError, statistics, refresh } = useDisasterData({
    autoRefresh: true,
    refreshInterval: 15 * 60 * 1000, // 15 minutes - reduced frequency for home page
    includeSignificantOnly: true,
  });

  // Check permissions
  const canViewReports = isAuthenticated && (isAdmin() || isCj());
  const canCreateReports = !isAuthenticated || !isOnlyUser();

  // Hero content with professional disaster management image
  const heroContent = {
    image: 'https://images.unsplash.com/photo-1638401607292-ba5ca538031e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
    title: 'Intelligent Emergency Response Solutions',
    subtitle: 'Harness the power of AI and real-time data to predict, prepare for, and respond to disasters more effectively. Connecting communities, first responders, and emergency resources when it matters most.',
    category: 'Advanced Disaster Management'
  };

  // Beautiful disaster statistics with enhanced design
  const stats = [
    {
      icon: AlertTriangle,
      value: statistics ? statistics.totalActive.toLocaleString() : (disastersLoading ? "..." : "2,847"),
      label: 'Active Incidents',
      description: 'Real-time monitoring',
      color: 'from-red-500 to-red-600',
      bgColor: 'from-red-50 to-red-100',
      iconColor: 'text-red-600'
    },
    {
      icon: Heart,
      value: statistics ? statistics.critical.toLocaleString() : (disastersLoading ? "..." : "156"),
      label: 'Critical Events',
      description: 'Urgent situations',
      color: 'from-purple-500 to-purple-600',
      bgColor: 'from-purple-50 to-purple-100',
      iconColor: 'text-purple-600'
    },
    {
      icon: CheckCircle,
      value: statistics ? statistics.high.toLocaleString() : (disastersLoading ? "..." : "89"),
      label: 'Resolved Today',
      description: 'Successful responses',
      color: 'from-emerald-500 to-emerald-600',
      bgColor: 'from-emerald-50 to-emerald-100',
      iconColor: 'text-emerald-600'
    },
    {
      icon: Globe,
      value: '24/7',
      label: 'Global Coverage',
      description: 'Always monitoring',
      color: 'from-blue-500 to-blue-600',
      bgColor: 'from-blue-50 to-blue-100',
      iconColor: 'text-blue-600'
    }
  ];

  // Clean, modern disaster management features
  const features = [
    {
      icon: Shield,
      title: 'Emergency Preparedness',
      description: 'AI-powered risk assessment and comprehensive emergency planning resources for maximum protection.',
      image: 'https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      gradient: 'from-blue-600 to-blue-700'
    },
    {
      icon: Users,
      title: 'Community Response Network',
      description: 'Connected emergency responders and community support networks sharing real-time insights.',
      image: 'https://images.unsplash.com/photo-1559027615-cd4628902d4a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      gradient: 'from-emerald-600 to-teal-600'
    },
    {
      icon: Zap,
      title: 'Instant Alert System',
      description: 'Lightning-fast emergency notifications with predictive analytics for proactive safety measures.',
      image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      gradient: 'from-purple-600 to-indigo-600'
    },
    {
      icon: MapPin,
      title: 'Real-Time Mapping',
      description: 'Advanced GIS mapping with live disaster tracking and resource allocation optimization.',
      image: 'https://images.unsplash.com/photo-1551632811-561732d1e306?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      gradient: 'from-orange-600 to-red-600'
    },
    {
      icon: Activity,
      title: 'Recovery Analytics',
      description: 'Data-driven recovery planning with progress tracking and resource optimization tools.',
      image: 'https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      gradient: 'from-cyan-600 to-blue-600'
    },
    {
      icon: Target,
      title: 'Precision Response',
      description: 'Targeted emergency response coordination with AI-powered resource deployment strategies.',
      image: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      gradient: 'from-indigo-600 to-purple-600'
    }
  ];

  // Recent verified disasters data
  const recentDisasters = [
    {
      id: 1,
      title: 'Earthquake M7.2 - Turkey',
      location: 'Kahramanmaraş, Turkey',
      severity: 'Critical',
      time: '2 hours ago',
      status: 'Active Response',
      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      verified: true
    },
    {
      id: 2,
      title: 'Wildfire - California',
      location: 'Los Angeles County, CA',
      severity: 'High',
      time: '4 hours ago',
      status: 'Contained 60%',
      image: 'https://images.unsplash.com/photo-1574482620811-1aa16ffe3c82?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      verified: true
    },
    {
      id: 3,
      title: 'Flood Warning - Bangladesh',
      location: 'Dhaka Division, Bangladesh',
      severity: 'Medium',
      time: '6 hours ago',
      status: 'Monitoring',
      image: 'https://images.unsplash.com/photo-1547036967-23d11aacaee0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      verified: true
    },
    {
      id: 4,
      title: 'Hurricane Category 3',
      location: 'Gulf of Mexico',
      severity: 'Critical',
      time: '8 hours ago',
      status: 'Tracking',
      image: 'https://images.unsplash.com/photo-1446776877081-d282a0f896e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      verified: true
    }
  ];



  // Auto-advance features slider
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentFeatureSlide((prev) => (prev + 1) % Math.ceil(features.length / 3));
    }, 6000);
    return () => clearInterval(timer);
  }, [features.length]);

  // Scroll functionality
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 300);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };



  const nextFeatureSlide = () => {
    setCurrentFeatureSlide((prev) => (prev + 1) % Math.ceil(features.length / 3));
  };

  const prevFeatureSlide = () => {
    setCurrentFeatureSlide((prev) => (prev - 1 + Math.ceil(features.length / 3)) % Math.ceil(features.length / 3));
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'Critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'High': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'Medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <main>
        {/* Hero Section */}
        <section className="relative h-screen overflow-hidden">
          {/* Background Image */}
          <div className="absolute inset-0">
            <img
              src={heroContent.image}
              alt={heroContent.title}
              className="w-full h-full object-cover object-center"
              loading="eager"
            />
            {/* Enhanced overlay for better text readability */}
            <div className="absolute inset-0 bg-gradient-to-r from-slate-900/90 via-slate-800/75 to-slate-900/85"></div>
            <div className="absolute inset-0 bg-gradient-to-b from-slate-900/30 via-transparent to-slate-900/70"></div>
            <div className="absolute inset-0 bg-gradient-to-t from-slate-900/50 via-transparent to-transparent"></div>
          </div>

          {/* Animated Background Elements */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-cyan-500/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
            <div className="absolute top-1/2 right-1/3 w-64 h-64 bg-indigo-500/5 rounded-full blur-2xl animate-pulse delay-500"></div>
          </div>

          {/* Hero Content */}
          <div className="relative z-10 h-full flex items-center">
            <div className="max-w-7xl mx-auto px-6 lg:px-8 w-full">
              <div className="grid lg:grid-cols-12 gap-8 items-center min-h-[80vh]">
                {/* Main Content - Left aligned on large screens, centered on mobile */}
                <div className="lg:col-span-8 xl:col-span-7 text-white text-center lg:text-left space-y-8">
                  {/* Trust Badge */}
                  <div className="inline-flex items-center px-5 py-3 rounded-full bg-white/15 backdrop-blur-xl border border-white/30 text-sm font-medium hover:bg-white/20 transition-all duration-300 shadow-xl">
                    <div className="w-2.5 h-2.5 bg-emerald-400 rounded-full mr-3 animate-pulse shadow-sm"></div>
                    <Globe size={16} className="mr-2 text-cyan-300" />
                    <span className="text-white font-semibold uppercase tracking-wide">Live Monitoring</span>
                  </div>

                  {/* Main Heading - Enhanced Typography */}
                  <h1 className="text-5xl lg:text-6xl xl:text-7xl font-bold leading-[1.1] tracking-tight">
                    <span className="block text-white mb-2 drop-shadow-2xl">Intelligent</span>
                    <span className="block text-white mb-2 drop-shadow-2xl">Emergency Response</span>
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-blue-400 to-indigo-400 drop-shadow-2xl">
                      Solutions
                    </span>
                  </h1>

                  {/* Enhanced Description */}
                  <p className="text-lg lg:text-xl text-white/95 leading-relaxed max-w-2xl font-light drop-shadow-lg">
                    Harness the power of AI and real-time data to predict, prepare for, and respond to disasters more effectively.
                    <span className="block mt-2 text-white/85">
                      Connecting communities, first responders, and emergency resources when it matters most.
                    </span>
                  </p>

                  {/* Enhanced Action Buttons */}
                  <div className="flex flex-col sm:flex-row gap-5">
                    {canCreateReports ? (
                      <Link
                        to="/report/new"
                        className="group bg-gradient-to-r from-red-600 to-red-700 text-white px-10 py-5 rounded-2xl text-base font-semibold hover:from-red-700 hover:to-red-800 transition-all duration-300 flex items-center justify-center shadow-2xl hover:shadow-red-500/25 hover:scale-105 transform"
                      >
                        <AlertTriangle size={20} className="mr-3" />
                        Report Emergency
                        <ArrowRight size={20} className="ml-3 group-hover:translate-x-1 transition-transform" />
                      </Link>
                    ) : (
                      <Link
                        to="/reports"
                        className="group bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-10 py-5 rounded-2xl text-base font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 flex items-center justify-center shadow-2xl hover:shadow-blue-500/25 hover:scale-105 transform"
                      >
                        <Heart size={20} className="mr-3" />
                        View Reports
                        <ArrowRight size={20} className="ml-3 group-hover:translate-x-1 transition-transform" />
                      </Link>
                    )}

                    <button className="group bg-white/15 backdrop-blur-xl border border-white/40 text-white px-10 py-5 rounded-2xl text-base font-semibold hover:bg-white/25 hover:border-white/50 transition-all duration-300 flex items-center justify-center hover:scale-105 shadow-2xl transform">
                      <Play size={20} className="mr-3" />
                      Watch Demo
                    </button>
                  </div>

                {/* Enhanced Trust Indicators */}
                <div className="lg:col-span-4 xl:col-span-5 lg:flex lg:justify-end">
                  <div className="grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-1 gap-6 lg:gap-8 text-white/90 lg:max-w-sm">
                    <div className="flex items-center space-x-3 group cursor-pointer">
                      <div className="p-3 rounded-xl bg-cyan-500/20 group-hover:bg-cyan-500/30 transition-all duration-300 shadow-lg">
                        <Clock className="text-cyan-300" size={20} />
                      </div>
                      <div>
                        <span className="font-semibold text-base block">24/7 Emergency Watch</span>
                        <span className="text-white/70 text-sm">Always monitoring</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3 group cursor-pointer">
                      <div className="p-3 rounded-xl bg-emerald-500/20 group-hover:bg-emerald-500/30 transition-all duration-300 shadow-lg">
                        <Shield className="text-emerald-300" size={20} />
                      </div>
                      <div>
                        <span className="font-semibold text-base block">Certified Safe</span>
                        <span className="text-white/70 text-sm">Trusted platform</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3 group cursor-pointer">
                      <div className="p-3 rounded-xl bg-blue-500/20 group-hover:bg-blue-500/30 transition-all duration-300 shadow-lg">
                        <Users className="text-blue-300" size={20} />
                      </div>
                      <div>
                        <span className="font-semibold text-base block">Global Network</span>
                        <span className="text-white/70 text-sm">Worldwide coverage</span>
                      </div>
                    </div>
                  </div>
                </div>
                </div>
              </div>
            </div>
          </div>


        </section>

        {/* Statistics Section */}
        <section className="py-32 bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/40 relative overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-100/50 rounded-full blur-3xl"></div>
            <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-indigo-100/50 rounded-full blur-3xl"></div>
          </div>
          
          <div className="relative max-w-7xl mx-auto px-6 lg:px-8">
            <div className="text-center mb-20">
              <div className="inline-flex items-center px-6 py-4 rounded-full bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-700 text-sm font-semibold mb-8 shadow-lg hover:shadow-xl transition-all duration-300">
                <TrendingUp size={18} className="mr-3" />
                Real-Time Disaster Intelligence
              </div>
              <h2 className="text-5xl lg:text-7xl font-black text-gray-900 mb-8 leading-tight">
                Saving Lives Through <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600">Smart Technology</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-light">
                Our advanced platform processes millions of data points daily to deliver 
                predictive insights and real-time monitoring for faster, more effective disaster response.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <div
                  key={index}
                  className="group bg-white rounded-2xl p-8 shadow-sm hover:shadow-lg hover:scale-[1.02] transition-all duration-300 border border-gray-100/80 hover:border-gray-200"
                >
                  <div className="text-center">
                    <div className={`inline-flex p-4 rounded-xl bg-gradient-to-br ${stat.color} text-white shadow-md mb-6 group-hover:scale-105 transition-all duration-300`}>
                      <stat.icon size={24} />
                    </div>
                    <div className="text-3xl font-bold text-gray-900 mb-2">
                      {stat.value}
                    </div>
                    <div className="text-gray-800 font-semibold text-base mb-1">{stat.label}</div>
                    <div className="text-gray-500 text-sm">{stat.description}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Recent Verified Disasters Section */}
        <section className="py-32 bg-gradient-to-br from-gray-50 via-white to-red-50/30 relative overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0">
            <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-red-100/40 rounded-full blur-3xl"></div>
            <div className="absolute bottom-1/4 left-1/4 w-80 h-80 bg-orange-100/40 rounded-full blur-3xl"></div>
          </div>
          
          <div className="relative max-w-7xl mx-auto px-6 lg:px-8">
            <div className="text-center mb-20">
              <div className="inline-flex items-center px-6 py-3 rounded-full bg-gradient-to-r from-red-100 to-orange-100 text-red-700 text-sm font-semibold mb-8 shadow-lg hover:shadow-xl transition-all duration-300">
                <AlertTriangle size={18} className="mr-2" />
                Recent Verified Disasters
              </div>
              <h2 className="text-5xl lg:text-7xl font-black text-gray-900 mb-8 leading-tight">
                Latest <span className="text-transparent bg-clip-text bg-gradient-to-r from-red-600 via-orange-600 to-red-700">Emergency Updates</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-light">
                Stay informed with real-time updates on verified disasters and emergency situations 
                from our global monitoring network with instant verification and response coordination.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {recentDisasters.map((disaster) => (
                <div
                  key={disaster.id}
                  className="group bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-lg hover:scale-[1.02] transition-all duration-300 border border-gray-100/80"
                >
                  <div className="aspect-[4/3] overflow-hidden relative">
                    <img
                      src={disaster.image}
                      alt={disaster.title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                    />
                    <div className="absolute top-3 right-3">
                      <span className={`px-3 py-1 rounded-full text-xs font-semibold border ${getSeverityColor(disaster.severity)}`}>
                        {disaster.severity}
                      </span>
                    </div>
                    {disaster.verified && (
                      <div className="absolute top-3 left-3">
                        <div className="bg-green-600 text-white px-2 py-1 rounded-full text-xs font-semibold flex items-center">
                          <CheckCircle size={12} className="mr-1" />
                          Verified
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-gray-900 mb-2">{disaster.title}</h3>
                    <div className="flex items-center text-gray-600 text-sm mb-2">
                      <MapIcon size={14} className="mr-1" />
                      {disaster.location}
                    </div>
                    <div className="flex items-center text-gray-600 text-sm mb-3">
                      <Calendar size={14} className="mr-1" />
                      {disaster.time}
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-blue-600">{disaster.status}</span>
                      <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                        View Details →
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="text-center mt-12">
              <Link
                to="/reports"
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-red-600 to-orange-600 text-white rounded-xl font-semibold hover:from-red-700 hover:to-orange-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105"
              >
                View All Disasters
                <ArrowRight size={20} className="ml-2" />
              </Link>
            </div>
          </div>
        </section>

        {/* Live Disaster Map Section */}
        <section className="py-24 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 relative overflow-hidden">
          {/* Animated Background Elements */}
          <div className="absolute inset-0">
            <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
            <div className="absolute top-1/2 right-1/3 w-64 h-64 bg-indigo-500/5 rounded-full blur-2xl animate-pulse delay-500"></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-6 lg:px-8">
            <div className="text-center mb-12">
              <div className="inline-flex items-center px-5 py-2 rounded-full bg-white/10 backdrop-blur-xl border border-white/20 text-sm font-semibold mb-6 text-white">
                <Globe size={16} className="mr-2" />
                Live Global Monitoring
              </div>
              <h2 className="text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight drop-shadow-lg">
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-indigo-300">
                  Real-Time Disaster Tracking
                </span>
              </h2>
              <p className="text-lg text-blue-100 max-w-3xl mx-auto leading-relaxed drop-shadow-sm">
                Monitor active disasters worldwide with our advanced mapping system powered by 
                real-time data from global monitoring networks.
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-12 items-center">
              {/* Left Side - Live Map Interface */}
              <div className="relative">
                <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
                  {/* Map Header */}
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-semibold text-white">Live Disaster Map</h3>
                    <div className="flex items-center space-x-3">
                      {!disastersLoading && !disastersError && (
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                          <span className="text-blue-300 text-sm font-medium">Live Updates</span>
                        </div>
                      )}
                      {disastersLoading && (
                        <div className="flex items-center space-x-2">
                          <RefreshCw className="w-4 h-4 text-blue-400 animate-spin" />
                          <span className="text-blue-300 text-sm font-medium">Loading...</span>
                        </div>
                      )}
                      {disastersError && (
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                          <span className="text-red-300 text-sm font-medium">Connection Error</span>
                        </div>
                      )}
                      <button
                        onClick={refresh}
                        className="bg-white/20 backdrop-blur-sm text-white p-2 rounded-lg hover:bg-white/30 transition-colors"
                        title="Refresh Data"
                      >
                        <RefreshCw className="w-4 h-4" />
                      </button>
                    </div>
                  </div>

                  {/* Real-World Disaster Map */}
                  <div className="relative">
                    {disastersError ? (
                      <div className="bg-slate-800 rounded-2xl h-80 flex items-center justify-center">
                        <div className="text-center text-white">
                          <AlertTriangle className="w-12 h-12 mx-auto mb-4 text-red-400" />
                          <p className="text-lg font-semibold mb-2">Unable to Load Map</p>
                          <p className="text-sm text-gray-300 mb-4">{disastersError}</p>
                          <button
                            onClick={refresh}
                            className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                          >
                            Try Again
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div className="bg-slate-800 rounded-2xl h-80 overflow-hidden">
                        <SimpleLeafletMap
                          disasters={disasters || []}
                          loading={disastersLoading}
                          className="w-full h-full rounded-2xl"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Right Side - Disaster Statistics */}
              <div className="space-y-6">
                <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-semibold text-white">Active Disasters</h3>
                    <div className="text-3xl font-bold text-white">
                      {statistics ? statistics.totalActive : (disastersLoading ? "..." : "2,847")}
                    </div>
                  </div>
                  <p className="text-blue-100 text-sm">Currently monitored worldwide</p>
                </div>

                <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-semibold text-white">Critical Events</h3>
                    <div className="text-3xl font-bold text-red-300">
                      {statistics ? statistics.critical : (disastersLoading ? "..." : "156")}
                    </div>
                  </div>
                  <p className="text-blue-100 text-sm">Requiring immediate attention</p>
                </div>

                <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-xl">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-semibold text-white">Response Teams</h3>
                    <div className="text-3xl font-bold text-emerald-300">
                      {statistics ? statistics.high : (disastersLoading ? "..." : "89")}
                    </div>
                  </div>
                  <p className="text-blue-100 text-sm">Active response operations</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section with Slider */}
        <section className="py-32 bg-gradient-to-br from-white via-slate-50 to-blue-50/40 relative overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0">
            <div className="absolute top-1/3 left-1/3 w-96 h-96 bg-blue-100/30 rounded-full blur-3xl"></div>
            <div className="absolute bottom-1/3 right-1/3 w-80 h-80 bg-indigo-100/30 rounded-full blur-3xl"></div>
          </div>
          
          <div className="relative max-w-7xl mx-auto px-6 lg:px-8">
            <div className="text-center mb-20">
              <h2 className="text-5xl lg:text-7xl font-black text-gray-900 mb-8 leading-tight">
                Comprehensive <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600">Disaster Solutions</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-light">
                From predictive analytics to emergency response and recovery, our comprehensive platform 
                delivers the intelligence and tools needed for effective disaster management worldwide.
              </p>
            </div>

            {/* Features Slider */}
            <div className="relative">
              <div className="overflow-hidden">
                <div 
                  className="flex transition-transform duration-500 ease-in-out"
                  style={{ transform: `translateX(-${currentFeatureSlide * 100}%)` }}
                >
                  {Array.from({ length: Math.ceil(features.length / 3) }).map((_, slideIndex) => (
                    <div key={slideIndex} className="w-full flex-shrink-0">
                      <div className="grid lg:grid-cols-3 gap-8">
                        {features.slice(slideIndex * 3, (slideIndex + 1) * 3).map((feature, index) => (
                          <div
                            key={slideIndex * 3 + index}
                            className="group bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-lg hover:scale-[1.02] transition-all duration-300 border border-gray-100/80"
                          >
                            <div className="aspect-[4/3] overflow-hidden">
                              <img
                                src={feature.image}
                                alt={feature.title}
                                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                              />
                            </div>
                            <div className="p-8">
                              <div className={`inline-flex p-3 rounded-xl bg-gradient-to-br ${feature.gradient} text-white shadow-md mb-4`}>
                                <feature.icon size={20} />
                              </div>
                              <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                              <p className="text-gray-600 leading-relaxed text-sm">{feature.description}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Slider Navigation */}
              <button
                onClick={prevFeatureSlide}
                className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white shadow-lg rounded-full p-3 hover:bg-gray-50 transition-colors duration-300"
              >
                <ChevronLeft size={24} className="text-gray-600" />
              </button>

              <button
                onClick={nextFeatureSlide}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white shadow-lg rounded-full p-3 hover:bg-gray-50 transition-colors duration-300"
              >
                <ChevronRight size={24} className="text-gray-600" />
              </button>

              {/* Slider Indicators */}
              <div className="flex justify-center space-x-2 mt-8">
                {Array.from({ length: Math.ceil(features.length / 3) }).map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentFeatureSlide(index)}
                    className={`transition-all duration-300 ${
                      index === currentFeatureSlide
                        ? 'w-8 h-2 bg-blue-600 rounded-full'
                        : 'w-2 h-2 bg-gray-300 rounded-full hover:bg-gray-400'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-32 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 relative overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0">
            <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-indigo-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
            <div className="absolute top-1/2 right-1/3 w-64 h-64 bg-purple-500/10 rounded-full blur-2xl animate-pulse delay-500"></div>
          </div>
          
          <div className="relative max-w-7xl mx-auto px-6 lg:px-8 text-center">
            <div className="max-w-5xl mx-auto">
              <h2 className="text-5xl lg:text-7xl font-black text-white mb-8 leading-tight drop-shadow-2xl">
                Ready to Transform Disaster Response?
              </h2>
              <p className="text-xl lg:text-2xl text-blue-100 mb-12 leading-relaxed font-light drop-shadow-lg">
                Join emergency responders and communities worldwide using our platform to enhance 
                safety, optimize response times, and save lives through intelligent disaster management.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <Link
                  to="/signup"
                  className="group relative bg-white text-slate-900 px-12 py-6 rounded-2xl text-xl font-bold hover:bg-gray-50 transition-all duration-300 flex items-center justify-center shadow-2xl hover:shadow-white/25 hover:scale-105 overflow-hidden"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-100/20 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                  <span className="relative z-10">Get Started Today</span>
                  <ArrowRight size={24} className="ml-3 group-hover:translate-x-1 transition-transform relative z-10" />
                </Link>
                <Link
                  to="/about"
                  className="group relative bg-white/10 backdrop-blur-2xl border border-white/30 text-white px-12 py-6 rounded-2xl text-xl font-bold hover:bg-white/20 transition-all duration-300 flex items-center justify-center shadow-2xl hover:shadow-white/10 hover:scale-105 overflow-hidden"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                  <span className="relative z-10">Learn More</span>
                </Link>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
      <ChatWidget />

      {/* Scroll to Top Button */}
      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-8 right-8 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors duration-300 z-50"
        >
          <ArrowRight size={20} className="transform -rotate-90" />
        </button>
      )}
    </div>
  );
};

export default Home;